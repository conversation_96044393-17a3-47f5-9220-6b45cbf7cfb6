import math
print("Welcome to the financial calculator program.")
print()
print("Please select from the following options:")
print()
#Financial calculators program
#A program that allows the users to calculate their investment or home loan repayment amounts

print("Investment - to calculate the amount of interest you'll earn on your investment.")
print("Bond       - to calculate the amout you'll have to pay on a home loan.")
print()

#Request the user to select an option from the options provided
users_option = input("Enter either 'investment' or 'bond' from the menu above to proceed: ").lower()
print()

#Confirm the users input and proceed with the appropraite calculation
if users_option == "investment":
    print("You have selected the investment calculator")
    print()
    
    #Request the user to input the investment details
    deposit = float(input("Enter the amount of money you are going to deposit: "))
    interest_rate = float(input("Enter the interest rate as a percentage: "))
    years = int(input("Enter the number of years you plan on investing: "))
    interest_type = input("Do you want 'simple' or 'compound' interest? ").lower()
    print()
    
    #convert the percentage to a decimal
    r = interest_rate / 100

    #calculate based on the interest type
    if interest_type == "simple":
        #simple interest formula - A = P * (1 + r * t)
        total_amount = deposit * (1 + r * years)
        print()
        print(f"Using simple interest, your investment will be worth: R{round(total_amount, 2)}")

    elif interest_type == "compound":
        #compound interest formula - A = P * math.pow((1 + r), t)
        total_amount = deposit * math.pow((1 + r), years)
        print()
        print(f"Using compund interest, your investment will be worth: R{round(total_amount, 2)}")

    else:
        print("An Error has occoured : ")
        print("Please restart the financial calculator and enter either 'simple' or 'compound' for interest type.")
    
elif users_option == "bond":
    print("You have selected the bond calculator")
    print()
    
    #request the user to input the bond details
    present_house_value = float(input("Enter the present value of the house: "))
    yearly_interest_rate = float(input("Enter the interest rate as a percentage: "))
    months = int(input("Enter the number of months to repay the bond: "))
    print()

    #calculate the monthly interest rate
    monthly_interest_rate = (yearly_interest_rate / 100) / 12

    #bond repayment formula - repayment = (i * P) / (1 -(1 + i) ** (-n))
    monthly_repayment = (monthly_interest_rate * present_house_value)/(1-(1+monthly_interest_rate)**(-months))
    print(f"Your monthly bond repayemnt will be: R{round(monthly_repayment, 2)}")

else:
    print("An Error has occoured :")
    print("Please restart the financial calculator and enter either 'investment' or 'bond' from the option menu.")


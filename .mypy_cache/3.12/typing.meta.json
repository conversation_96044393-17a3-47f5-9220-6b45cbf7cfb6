{"data_mtime": 1756150393, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 499, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 30], "dependencies": ["collections", "sys", "typing_extensions", "_collections_abc", "_typeshed", "abc", "re", "types", "contextlib", "builtins", "_frozen_importlib"], "hash": "1850e3dd2e43ae76f25c8c4ac58581fb9f659a15", "id": "typing", "ignore_all": true, "interface_hash": "e99619c8c62209d75f49a25d4d23bed9c3b817ae", "mtime": 1756070762, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "linux", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/home/<USER>/.vscode-server/extensions/ms-python.mypy-type-checker-2025.2.0/bundled/libs/mypy/typeshed/stdlib/typing.pyi", "plugin_data": null, "size": 37819, "suppressed": [], "version_id": "1.15.0"}
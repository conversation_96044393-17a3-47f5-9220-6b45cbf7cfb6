#Write a program that continually asks the user to input a number.
#When the user enters -1, the program stops.
#It then calculate the average of all valid numbers entered (excluding -1 and 0)

total = 0 #To store the sum of valid numbers
count = 0 #To count how many valid numbers were enetered

number = int(input("Please enter a number: "))

while number != -1:
    if number != 0:
        total += number
        count += 1
    number = int(input('Please enter a number: '))

if count > 0:
    average = total/count
    print(f'The average of valid numbers is: {average}')
else:
    print('No valid numbers were enetered, the average cannot be calculated.')
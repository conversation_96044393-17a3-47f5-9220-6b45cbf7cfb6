#design a program that determines the award recieved for completeing a triathelon

#accept user inputs for the times(in minutes) of all three triathlon events
swimming_time = int(input("Enter your swiming time in minutes: "))
cycling_time = int(input("Enter your cycling time in minutes: "))
running_time = int(input("Enter your running time in minutes: "))

#calculate and output the total time to complete the triathelon
total_time = swimming_time + cycling_time + running_time
print(f"Your total time is {total_time} minutes.")

#determine the award the participant recieves based on the total time
if total_time >= 0 and total_time <= 100 :
    award = "provincial colours."
elif total_time >= 101 and total_time <= 105 :
    award = "provincial half colours."
elif total_time >= 106 and total_time <= 110 :
    award = "provincial scroll."
else :
    award = "no award."

#output the award the participant recieves.
print(f"Your award you recieve is {award}.")    
#!/usr/bin/env python3
"""
String Handling Task - Alternative Character Case Conversion

This program demonstrates two different string manipulation techniques:
1. Alternating character case (uppercase/lowercase for each character)
2. Alternating word case (uppercase/lowercase for each word)
"""

def alternate_character_case(text):
    """
    Convert each alternate character to uppercase/lowercase.
    Even indices (0, 2, 4, ...) become uppercase
    Odd indices (1, 3, 5, ...) become lowercase
    
    Args:
        text (str): Input string to process
        
    Returns:
        str: String with alternating character cases
    """
    result = ""
    for i, char in enumerate(text):
        if i % 2 == 0:  # Even index - uppercase
            result += char.upper()
        else:  # Odd index - lowercase
            result += char.lower()
    return result

def alternate_word_case(text):
    """
    Convert each alternate word to uppercase/lowercase.
    Even word indices (0, 2, 4, ...) become lowercase
    Odd word indices (1, 3, 5, ...) become uppercase
    
    Args:
        text (str): Input string to process
        
    Returns:
        str: String with alternating word cases
    """
    # Split the string into words
    words = text.split()
    
    # Process each word based on its index
    for i in range(len(words)):
        if i % 2 == 0:  # Even index - lowercase
            words[i] = words[i].lower()
        else:  # Odd index - uppercase
            words[i] = words[i].upper()
    
    # Join the words back together with spaces
    return " ".join(words)

def main():
    """Main function to run the string manipulation program."""
    print("String Handling - Alternative Character Case Conversion")
    print("=" * 55)
    
    # Get input from user
    user_input = input("Please enter a string: ")
    
    print(f"\nOriginal string: '{user_input}'")
    print()
    
    # Task 1: Alternate character case
    char_result = alternate_character_case(user_input)
    print("Task 1 - Alternating character case:")
    print(f"Result: '{char_result}'")
    print("(Even positions uppercase, odd positions lowercase)")
    print()
    
    # Task 2: Alternate word case
    word_result = alternate_word_case(user_input)
    print("Task 2 - Alternating word case:")
    print(f"Result: '{word_result}'")
    print("(Even word positions lowercase, odd word positions uppercase)")
    print()
    
    # Demonstrate with the examples from the task description
    print("Examples from task description:")
    print("-" * 35)
    
    # Example 1: "Hello World" -> "HeLlO WoRlD"
    example1 = "Hello World"
    example1_result = alternate_character_case(example1)
    print(f"Character alternating: '{example1}' -> '{example1_result}'")
    
    # Example 2: "I am learning to code" -> "i AM learning TO code"
    example2 = "I am learning to code"
    example2_result = alternate_word_case(example2)
    print(f"Word alternating: '{example2}' -> '{example2_result}'")

if __name__ == "__main__":
    main()

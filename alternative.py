# Get input from user
user_string = input("Please enter a string: ")

print(f"Original string: {user_string}")

# Task 1: Make each alternate character uppercase and lowercase
# Even positions (0, 2, 4, ...) will be uppercase
# Odd positions (1, 3, 5, ...) will be lowercase

alternate_string = ""

for i in range(len(user_string)):
    if i % 2 == 0:  # Even position - uppercase
        alternate_string = alternate_string + user_string[i].upper()
    else:  # Odd position - lowercase
        alternate_string = alternate_string + user_string[i].lower()

print(f"Alternating character case: {alternate_string}")

# Task 2: Make each alternative word lowercase and uppercase
# Split the string into words
words = user_string.split()

# Process each word
for i in range(len(words)):
    if i % 2 == 0:  # Even word position - lowercase
        words[i] = words[i].lower()
    else:  # Odd word position - uppercase
        words[i] = words[i].upper()

# Join the words back together
alternate_words = " ".join(words)

print(f"Alternating word case: {alternate_words}")

#Print welcome message
print ("Welcome to the error program") #Fixed syntax error by adding parentheses
print ("\n")

# Variables declaring the user's age, casting the str to an int, and printing the result
age_Str = "24"  # Syntax error fixed by removing "years old" to match the int conversion
age = int(age_Str) 
print("I'm " + str(age) + "years old.") #syntax error fixed by adding a space after "I'm"

# Variables declaring additional years and printing the total years of age
years_from_now = 3 #logical error fixed by removing int() since years_from_now is already an int
total_years = age + years_from_now

print ("The total number of years:" + str(total_years)) #syntax error str() needed to convert total_years to string 

# Variable to calculate the total number of months from the given number of years and printing the result
total_months = total_years * 12 + 6  #logical error fixed by adding 6 months to the total years
print ("In 3 years and 6 months, I'll be " + str(total_months) + " months old") #syntax error fixed by converting total_months to str 

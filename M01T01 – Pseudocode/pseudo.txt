scenario 1 -

set the largest_number to zero
request a number from the user
while number is not equal to zero
    if number is greater than the largest_number
        set the largest_number to number 
    request a number from the user 		
print largest_number

(I used example 4 to help me while loop)


scenario 2 -

request user's name
store input in variable called first_name
print first_name along with "Hello, <PERSON>"

(I used example 2 on page 7 to assist me in answering this)

scenario 3 -

set total to zero
set count to zero
request a number from user
while user wants to enter somemore numbers
    add number to total
    add 1 to count 
    request a number form user
calculate the average by dividing the total by the count 	
print average

I used the example 4 on page 7 in the course guide and changed it for arbitary numbers in my algorithm answer, i also used google to define a arithmetic average


scenario 4 -

request grocery list from the user
sort the grocery list in alphabetical order
print each product in the grocery list


scenario 5 -

read X and Y from user
calculate S by adding X and Y together
calculate A by diving S by two
calculate P by multipling X by Y
write S, A, P

I used the flowchart symbols section provided in the course guide to work out the algorithm
# PLEASE ENSURE YOU OPEN THIS FILE IN VSCode

# *** NOTE ON COMMENTS ***
# This is a comment in Python.
# Comments can be placed anywhere in Python code and the computer ignores them -- they are intended to be read by humans.
# Any line with a # in front of it is a comment.
# Please read all the comments in this example file and all others.


# =============  Writing if statements ==================
# if statements are a type of control structure which control the flow of logic within a program.
# if statements contain a condition.
# Conditions are statements that can only evaluate to a boolean value, True or False.
# If the condition is True then the indented statements are executed; if the condition is False then the indented statements are skipped.
# In Python, if statements have the following general syntax:
#               if condition :
#                       indented statements


# ************ Example 1 ************
age = 20
if age >= 18:
    print("You're over 18 and can come in.")

# Explanation:
# We check if the age is greater than or equal to 18.
# If the age is 18 or older, then print out "You're over 18 and can come in."
# If they are not 18 or older, then the print statement is skipped and nothing is printed.
# As it stands this code will always produce the same output. We could change it to accept 
# input from a user to set the age, which would result in a more dynamic and useful program

# ****************** END OF EXAMPLE CODE ********************* #

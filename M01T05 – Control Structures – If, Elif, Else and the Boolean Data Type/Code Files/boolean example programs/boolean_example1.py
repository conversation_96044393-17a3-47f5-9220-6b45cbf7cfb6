# PLEASE ENSURE YOU OPEN THIS FILE IN VS CODE otherwise you may not be able to read/edit it.

# *** NOTE ON COMMENTS ***
# This is a comment in Python.
# Comments can be placed anywhere in Python code and the computer ignores them -- they are intended to be read by humans.
# Any line with a # in front of it is a comment.
# Please read all the comments in this example file and all others.


# ========= Declaring Boolean Variables  =========
# Boolean variables


# ************ Example 1 ************

has_drivers_licence = True
has_traffic_fine = False

# Please note when assigning either True or False to a variable don't forget to capitalise the first letter.
# Python is case sensitive which means, true, True and TRUE, mean three diffrent things!


# ========= Booleans in if Statements =========
# The if statement is an example of a control statement.
# A control statement is a statement that determines whether or not other statements will be executed.
# The if statement contains a condition that evaluates to either True or False which is a Boolean value.
# This determines whether or not the indented statements will be executed.


# ************ Example 2 ************

print("Example 2: ")

is_sunny = True

if is_sunny:
    print("It is sunny today, don't forget the sunscreen!")

# Explanation:
# Since is_sunny is True the print statement will execute and print out "It is sunny today, don't forget the sunscreen!”
# If is_sunny is changed to False the print statement will not execute and nothing will be printed.

# ****************** END OF EXAMPLE CODE ********************* #
